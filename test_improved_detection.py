#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的平行线检测测试脚本
测试修改后的算法是否能正确检测平行线
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector

def create_simple_test_image():
    """
    创建一个简单的测试图像，包含明显的平行线
    """
    # 创建白色背景
    height, width = 400, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 第一组：3条水平平行线
    y_positions = [80, 130, 180]
    for y in y_positions:
        cv2.line(image, (50, y), (550, y), (0, 0, 0), 4)
    
    # 第二组：2条垂直平行线
    x_positions = [200, 300]
    for x in x_positions:
        cv2.line(image, (x, 250), (x, 350), (0, 0, 0), 4)
    
    # 第三组：2条倾斜平行线
    cv2.line(image, (100, 250), (200, 300), (0, 0, 0), 4)
    cv2.line(image, (150, 250), (250, 300), (0, 0, 0), 4)
    
    return image

def create_complex_test_image():
    """
    创建一个复杂的测试图像
    """
    height, width = 500, 700
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 第一组：4条水平平行线
    y_positions = [60, 100, 140, 180]
    for y in y_positions:
        cv2.line(image, (30, y), (670, y), (0, 0, 0), 3)
    
    # 第二组：3条垂直平行线
    x_positions = [150, 250, 350]
    for x in x_positions:
        cv2.line(image, (x, 220), (x, 450), (0, 0, 0), 3)
    
    # 第三组：3条倾斜平行线（45度）
    starts = [(400, 250), (450, 250), (500, 250)]
    ends = [(500, 350), (550, 350), (600, 350)]
    for start, end in zip(starts, ends):
        cv2.line(image, start, end, (0, 0, 0), 3)
    
    # 添加一些噪声线（非平行）
    cv2.line(image, (50, 300), (150, 400), (128, 128, 128), 2)
    cv2.line(image, (600, 50), (650, 200), (128, 128, 128), 2)
    
    return image

def test_detection(image, image_name, detector):
    """
    测试平行线检测
    """
    print(f"\n{'='*60}")
    print(f"测试图像: {image_name}")
    print(f"{'='*60}")
    
    # 保存测试图像
    test_image_path = f"test_{image_name}.jpg"
    cv2.imwrite(test_image_path, image)
    print(f"测试图像已保存: {test_image_path}")
    
    try:
        # 执行检测
        parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(
            test_image_path, debug=True
        )
        
        # 输出结果
        print(f"\n=== 检测结果 ===")
        print(f"检测到的直线总数: {len(all_lines)}")
        print(f"平行线组数: {len(parallel_groups)}")
        
        if parallel_groups:
            total_parallel_lines = sum(len(group) for group in parallel_groups)
            total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
            print(f"平行线条数: {total_parallel_lines}")
            print(f"平行线对数: {total_pairs}")
            
            for idx, (group, pair_cnt) in enumerate(zip(parallel_groups, group_pairs)):
                angle = group[0][1]['angle']
                print(f"  第{idx+1}组: {len(group)} 条线, {pair_cnt} 对, 角度: {angle:.1f}°")
        else:
            print("未检测到平行线组")
        
        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path=f"result_{image_name}.jpg",
            show_all_lines=True
        )
        
        # 创建对比图
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        plt.title(f"原始图像 - {image_name}")
        plt.axis('off')
        
        plt.subplot(1, 3, 2)
        plt.imshow(edges, cmap='gray')
        plt.title("边缘检测结果")
        plt.axis('off')
        
        plt.subplot(1, 3, 3)
        plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        plt.title(f"检测结果 ({len(parallel_groups)} 组)")
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(f"comparison_{image_name}.png", dpi=150, bbox_inches='tight')
        plt.show()
        
        return len(parallel_groups) > 0
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    print("🔍 改进的平行线检测算法测试")
    print("="*60)
    
    # 创建检测器实例，使用更宽松的参数
    detector = ParallelLineDetector(
        angle_threshold=5.0,      # 角度阈值
        distance_threshold=100,   # 距离阈值
        min_line_length=30,       # 最小线段长度
        max_line_gap=15          # 最大间隙
    )
    
    # 测试简单图像
    simple_image = create_simple_test_image()
    success1 = test_detection(simple_image, "simple", detector)
    
    # 测试复杂图像
    complex_image = create_complex_test_image()
    success2 = test_detection(complex_image, "complex", detector)
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结:")
    print(f"简单图像检测: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"复杂图像检测: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 所有测试通过！算法改进成功。")
    elif success1 or success2:
        print("⚠️  部分测试通过，算法需要进一步调优。")
    else:
        print("❌ 所有测试失败，需要检查算法实现。")
    
    print("\n生成的文件:")
    print("- test_simple.jpg / test_complex.jpg: 测试图像")
    print("- result_simple.jpg / result_complex.jpg: 检测结果")
    print("- comparison_simple.png / comparison_complex.png: 对比图")
    print("- debug_edges.jpg: 边缘检测调试图")

if __name__ == "__main__":
    main()
