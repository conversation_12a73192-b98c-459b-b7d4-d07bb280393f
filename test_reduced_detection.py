#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试减少误检测的改进算法
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector

def test_with_real_image():
    """
    使用真实图像测试检测效果
    """
    print("🔍 测试减少误检测的改进算法")
    print("="*50)
    
    # 创建检测器，使用更严格的参数
    detector = ParallelLineDetector(
        angle_threshold=2.0,      # 降低角度阈值，更严格
        distance_threshold=100,   # 合理的距离阈值
        min_line_length=50,       # 增加最小线段长度
        max_line_gap=10          # 减少最大间隙
    )
    
    # 测试图像路径
    test_image_path = "D:/1/2.jpg"
    
    try:
        print(f"正在检测图像: {test_image_path}")
        
        # 执行检测
        result = detector.detect_parallel_lines(test_image_path, debug=True)
        parallel_groups, original_image, edges, gray, all_lines = result
        
        # 输出详细结果
        print(f"\n=== 检测结果统计 ===")
        print(f"原始检测线条数: {len(all_lines)}")
        print(f"平行线组数: {len(parallel_groups)}")
        
        if parallel_groups:
            total_parallel_lines = sum(len(group) for group in parallel_groups)
            total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
            print(f"平行线条数: {total_parallel_lines}")
            print(f"平行线对数: {total_pairs}")
            
            print(f"\n=== 各组详细信息 ===")
            for idx, (group, pair_cnt) in enumerate(zip(parallel_groups, group_pairs)):
                angle = group[0][1]['angle']
                distances = []
                for i in range(len(group)):
                    for j in range(i+1, len(group)):
                        dist = detector.calculate_distance_between_parallel_lines(
                            group[i][1], group[j][1]
                        )
                        distances.append(dist)
                
                avg_distance = np.mean(distances) if distances else 0
                print(f"第{idx+1}组:")
                print(f"  - 线条数: {len(group)}")
                print(f"  - 角度: {angle:.1f}°")
                print(f"  - 平均间距: {avg_distance:.1f}像素")
                print(f"  - 平行线对数: {pair_cnt}")
        else:
            print("未检测到平行线组")
        
        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="improved_result.jpg",
            show_all_lines=True
        )
        
        # 创建详细的对比图
        plt.figure(figsize=(20, 10))
        
        # 原始图像
        plt.subplot(2, 4, 1)
        plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        plt.title("原始图像")
        plt.axis('off')
        
        # 灰度图像
        plt.subplot(2, 4, 2)
        plt.imshow(gray, cmap='gray')
        plt.title("灰度图像")
        plt.axis('off')
        
        # 边缘检测结果
        plt.subplot(2, 4, 3)
        plt.imshow(edges, cmap='gray')
        plt.title(f"边缘检测")
        plt.axis('off')
        
        # 检测结果
        plt.subplot(2, 4, 4)
        plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        plt.title(f"检测结果\n({len(parallel_groups)}组, {len(all_lines)}条线)")
        plt.axis('off')
        
        # 只显示平行线组（不显示所有线条）
        clean_result = detector.visualize_results(
            original_image,
            parallel_groups,
            None,  # 不显示所有线条
            save_path="clean_result.jpg",
            show_all_lines=False
        )
        
        plt.subplot(2, 4, 5)
        plt.imshow(cv2.cvtColor(clean_result, cv2.COLOR_BGR2RGB))
        plt.title(f"仅平行线组\n({len(parallel_groups)}组)")
        plt.axis('off')
        
        # 统计信息图表
        if parallel_groups:
            plt.subplot(2, 4, 6)
            group_sizes = [len(group) for group in parallel_groups]
            plt.bar(range(1, len(group_sizes)+1), group_sizes)
            plt.title("各组线条数量")
            plt.xlabel("组号")
            plt.ylabel("线条数")
            
            plt.subplot(2, 4, 7)
            angles = [group[0][1]['angle'] for group in parallel_groups]
            plt.bar(range(1, len(angles)+1), angles)
            plt.title("各组角度")
            plt.xlabel("组号")
            plt.ylabel("角度(度)")
        
        plt.tight_layout()
        plt.savefig("detection_analysis.png", dpi=150, bbox_inches='tight')
        plt.show()
        
        # 评估检测质量
        print(f"\n=== 检测质量评估 ===")
        if len(all_lines) > 100:
            print("⚠️  检测到的线条过多，可能存在误检测")
        elif len(all_lines) < 5:
            print("⚠️  检测到的线条过少，可能遗漏了一些线条")
        else:
            print("✅ 检测到的线条数量合理")
        
        if len(parallel_groups) == 0:
            print("❌ 未检测到平行线组")
        elif len(parallel_groups) > 10:
            print("⚠️  检测到的平行线组过多，可能存在误检测")
        else:
            print("✅ 检测到的平行线组数量合理")
        
        print(f"\n✅ 检测完成!")
        print(f"生成文件:")
        print(f"- improved_result.jpg: 完整检测结果")
        print(f"- clean_result.jpg: 仅平行线组结果")
        print(f"- detection_analysis.png: 检测分析图")
        print(f"- debug_edges.jpg: 边缘检测调试图")
        
        return len(parallel_groups) > 0 and len(all_lines) <= 50
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_test():
    """
    创建简单测试图像验证算法
    """
    print("\n创建简单测试图像...")
    
    # 创建简单的测试图像
    height, width = 400, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 第一组：3条水平平行线
    y_positions = [100, 150, 200]
    for y in y_positions:
        cv2.line(image, (50, y), (550, y), (0, 0, 0), 3)
    
    # 第二组：2条垂直平行线
    x_positions = [200, 300]
    for x in x_positions:
        cv2.line(image, (x, 250), (x, 350), (0, 0, 0), 3)
    
    cv2.imwrite("simple_test_clean.jpg", image)
    
    # 测试简单图像
    detector = ParallelLineDetector(
        angle_threshold=2.0,
        distance_threshold=100,
        min_line_length=50,
        max_line_gap=10
    )
    
    result = detector.detect_parallel_lines("simple_test_clean.jpg", debug=False)
    parallel_groups, _, _, _, all_lines = result
    
    print(f"简单测试结果: {len(parallel_groups)}组平行线, {len(all_lines)}条线")
    
    return len(parallel_groups) == 2  # 期望检测到2组平行线

def main():
    """
    主测试函数
    """
    # 测试简单图像
    simple_success = create_simple_test()
    
    # 测试真实图像
    real_success = test_with_real_image()
    
    print(f"\n{'='*50}")
    print("测试总结:")
    print(f"简单图像测试: {'✅ 通过' if simple_success else '❌ 失败'}")
    print(f"真实图像测试: {'✅ 通过' if real_success else '❌ 失败'}")
    
    if simple_success and real_success:
        print("🎉 所有测试通过！误检测问题已解决。")
    else:
        print("⚠️  部分测试未通过，需要进一步调优。")

if __name__ == "__main__":
    main()
