#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试粗线条检测的脚本
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector

def create_thick_lines_test():
    """
    创建包含粗线条的测试图像
    """
    height, width = 500, 700
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 第一组：3条粗的水平平行线
    y_positions = [100, 200, 300]
    for y in y_positions:
        cv2.line(image, (50, y), (650, y), (0, 0, 0), 15)  # 很粗的线条
    
    # 第二组：2条粗的垂直平行线
    x_positions = [200, 400]
    for x in x_positions:
        cv2.line(image, (x, 350), (x, 480), (0, 0, 0), 12)  # 粗的垂直线
    
    # 第三组：2条粗的倾斜平行线
    cv2.line(image, (450, 100), (600, 200), (0, 0, 0), 10)  # 粗的倾斜线
    cv2.line(image, (500, 100), (650, 200), (0, 0, 0), 10)
    
    return image

def create_mixed_thickness_test():
    """
    创建包含不同粗细线条的测试图像
    """
    height, width = 400, 600
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 细线条组
    cv2.line(image, (50, 80), (550, 80), (0, 0, 0), 2)
    cv2.line(image, (50, 100), (550, 100), (0, 0, 0), 2)
    
    # 中等粗细线条组
    cv2.line(image, (50, 150), (550, 150), (0, 0, 0), 6)
    cv2.line(image, (50, 180), (550, 180), (0, 0, 0), 6)
    
    # 很粗的线条组
    cv2.line(image, (50, 250), (550, 250), (0, 0, 0), 20)
    cv2.line(image, (50, 300), (550, 300), (0, 0, 0), 20)
    
    return image

def test_thick_line_detection():
    """
    测试粗线条检测
    """
    print("🔍 测试粗线条检测算法")
    print("="*50)
    
    # 创建检测器，调整参数以适应粗线条
    detector = ParallelLineDetector(
        angle_threshold=3.0,
        distance_threshold=150,  # 增加距离阈值
        min_line_length=30,
        max_line_gap=20
    )
    
    # 测试1：纯粗线条
    print("\n测试1: 纯粗线条")
    thick_image = create_thick_lines_test()
    cv2.imwrite("thick_lines_test.jpg", thick_image)
    
    try:
        result = detector.detect_parallel_lines("thick_lines_test.jpg", debug=True)
        parallel_groups, image, edges, gray, all_lines = result
        
        print(f"检测结果:")
        print(f"- 总线条数: {len(all_lines)}")
        print(f"- 平行线组数: {len(parallel_groups)}")
        
        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']
            merged_info = [line.get('merged_count', 1) for _, line in group]
            print(f"- 第{i+1}组: {len(group)}条线, 角度{angle:.1f}°, 合并数{merged_info}")
        
        # 可视化
        result_img = detector.visualize_results(
            image, parallel_groups, all_lines, 
            "thick_lines_result.jpg", show_all_lines=True
        )
        
    except Exception as e:
        print(f"错误: {e}")
    
    # 测试2：混合粗细线条
    print("\n测试2: 混合粗细线条")
    mixed_image = create_mixed_thickness_test()
    cv2.imwrite("mixed_thickness_test.jpg", mixed_image)
    
    try:
        result = detector.detect_parallel_lines("mixed_thickness_test.jpg", debug=True)
        parallel_groups, image, edges, gray, all_lines = result
        
        print(f"检测结果:")
        print(f"- 总线条数: {len(all_lines)}")
        print(f"- 平行线组数: {len(parallel_groups)}")
        
        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']
            merged_info = [line.get('merged_count', 1) for _, line in group]
            print(f"- 第{i+1}组: {len(group)}条线, 角度{angle:.1f}°, 合并数{merged_info}")
        
        # 可视化
        result_img = detector.visualize_results(
            image, parallel_groups, all_lines, 
            "mixed_thickness_result.jpg", show_all_lines=True
        )
        
        # 创建对比图
        plt.figure(figsize=(15, 10))
        
        # 粗线条测试
        plt.subplot(2, 3, 1)
        plt.imshow(cv2.cvtColor(thick_image, cv2.COLOR_BGR2RGB))
        plt.title("粗线条测试图")
        plt.axis('off')
        
        plt.subplot(2, 3, 2)
        thick_result = cv2.imread("thick_lines_result.jpg")
        if thick_result is not None:
            plt.imshow(cv2.cvtColor(thick_result, cv2.COLOR_BGR2RGB))
        plt.title("粗线条检测结果")
        plt.axis('off')
        
        # 混合粗细测试
        plt.subplot(2, 3, 4)
        plt.imshow(cv2.cvtColor(mixed_image, cv2.COLOR_BGR2RGB))
        plt.title("混合粗细测试图")
        plt.axis('off')
        
        plt.subplot(2, 3, 5)
        plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        plt.title("混合粗细检测结果")
        plt.axis('off')
        
        # 边缘检测结果
        plt.subplot(2, 3, 3)
        edges_debug = cv2.imread("debug_edges.jpg", 0)
        if edges_debug is not None:
            plt.imshow(edges_debug, cmap='gray')
        plt.title("边缘检测结果")
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig("thick_lines_comparison.png", dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"\n✅ 测试完成!")
        print(f"生成文件:")
        print(f"- thick_lines_test.jpg: 粗线条测试图")
        print(f"- mixed_thickness_test.jpg: 混合粗细测试图")
        print(f"- thick_lines_result.jpg: 粗线条检测结果")
        print(f"- mixed_thickness_result.jpg: 混合粗细检测结果")
        print(f"- thick_lines_comparison.png: 对比图")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_thick_line_detection()
