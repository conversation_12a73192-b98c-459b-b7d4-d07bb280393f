# 图片平行线检测算法

这是一个基于OpenCV和Python的图片平行线检测算法，能够自动识别图片中的平行线并计算平行线组的数量。

## 功能特点

- 🔍 **自动检测**: 使用霍夫变换自动检测图片中的直线
- 📐 **平行线识别**: 基于角度和距离阈值识别平行线组
- 🎨 **可视化结果**: 用不同颜色标记不同的平行线组
- ⚙️ **参数可调**: 可调整角度阈值和距离阈值以适应不同场景
- 📊 **详细统计**: 输出每组平行线的数量和角度信息

## 算法原理

1. **图像预处理**: 转换为灰度图，高斯模糊去噪，Canny边缘检测
2. **直线检测**: 使用霍夫变换检测图像中的直线
3. **角度计算**: 将极坐标形式的直线转换为角度信息
4. **平行线分组**: 
   - 按角度相似性进行初步分组
   - 在每个角度组内按距离进行细分
   - 过滤掉少于2条线的组

## 环境要求

- Python 3.7+
- OpenCV
- NumPy
- Matplotlib

## 安装步骤

### 1. 创建conda环境

```bash
# 创建新的conda环境
conda create -n parallel_detection python=3.9

# 激活环境
conda activate parallel_detection
```

### 2. 安装依赖

```bash
# 安装OpenCV
conda install opencv

# 或者使用pip安装所有依赖
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```python
from parallel_line_detector import ParallelLineDetector

# 创建检测器实例
detector = ParallelLineDetector(
    angle_threshold=5.0,    # 角度阈值（度）
    distance_threshold=50   # 距离阈值（像素）
)

# 检测平行线
parallel_groups, original_image, edges = detector.detect_parallel_lines("your_image.jpg")

# 输出结果
print(f"检测到 {len(parallel_groups)} 组平行线")
for i, group in enumerate(parallel_groups):
    print(f"第 {i+1} 组: {len(group)} 条平行线")
```

### 运行测试

```bash
# 运行测试脚本（会创建测试图像并进行检测）
python test_parallel_detection.py
```

### 参数说明

- `angle_threshold`: 角度阈值，用于判断两条线是否平行（默认5.0度）
- `distance_threshold`: 距离阈值，用于判断平行线是否属于同一组（默认50像素）

## 输出文件

运行后会生成以下文件：

- `test_image.jpg`: 测试用的示例图像
- `test_result.jpg`: 标记了平行线组的结果图像
- `detection_process.png`: 显示检测过程的对比图

## 算法参数调优

### 角度阈值 (angle_threshold)
- **较小值 (1-3度)**: 对平行线的要求更严格，适合精确的几何图形
- **较大值 (5-10度)**: 容忍度更高，适合手绘图或有噪声的图像

### 距离阈值 (distance_threshold)
- **较小值 (20-40像素)**: 只将距离很近的平行线归为一组
- **较大值 (50-100像素)**: 将距离较远的平行线也归为一组

## 示例结果

算法能够检测以下类型的平行线：

- ✅ 水平平行线
- ✅ 垂直平行线  
- ✅ 倾斜平行线
- ✅ 多组不同角度的平行线

## 注意事项

1. **图像质量**: 清晰的边缘有助于提高检测精度
2. **线条粗细**: 过细的线条可能无法被检测到
3. **背景噪声**: 复杂背景可能产生误检，建议预处理
4. **参数调整**: 根据具体应用场景调整阈值参数

## 扩展功能

可以根据需要扩展以下功能：

- 支持曲线的平行检测
- 添加线条长度过滤
- 支持更复杂的几何形状检测
- 批量处理多张图片

## 故障排除

### 常见问题

1. **检测不到直线**: 
   - 检查图像质量和对比度
   - 调整Canny边缘检测参数
   - 降低霍夫变换的阈值

2. **平行线分组不准确**:
   - 调整angle_threshold参数
   - 调整distance_threshold参数

3. **误检过多**:
   - 提高霍夫变换阈值
   - 增加图像预处理步骤
