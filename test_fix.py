#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的平行线检测算法
"""

import cv2
import numpy as np
from parallel_line_detector import ParallelLineDetector

def create_test_image():
    """
    创建简单的测试图像
    """
    height, width = 300, 400
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 绘制几条平行线
    cv2.line(image, (50, 50), (350, 50), (0, 0, 0), 3)
    cv2.line(image, (50, 100), (350, 100), (0, 0, 0), 3)
    cv2.line(image, (50, 150), (350, 150), (0, 0, 0), 3)
    
    return image

def test_basic_functionality():
    """
    测试基本功能
    """
    print("🔍 测试修复后的平行线检测算法")
    print("="*40)
    
    # 创建测试图像
    test_image = create_test_image()
    cv2.imwrite("test_fix_image.jpg", test_image)
    print("✅ 测试图像已创建")
    
    # 创建检测器
    detector = ParallelLineDetector(
        angle_threshold=3.0,
        distance_threshold=80,
        min_line_length=30,
        max_line_gap=10
    )
    print("✅ 检测器已创建")
    
    try:
        # 执行检测
        print("开始检测...")
        result = detector.detect_parallel_lines("test_fix_image.jpg", debug=True)
        parallel_groups, original_image, edges, gray, all_lines = result
        
        print(f"\n=== 检测结果 ===")
        print(f"检测到的线条总数: {len(all_lines)}")
        print(f"平行线组数: {len(parallel_groups)}")
        
        if parallel_groups:
            for i, group in enumerate(parallel_groups):
                angle = group[0][1]['angle']
                print(f"第{i+1}组: {len(group)}条线, 角度: {angle:.1f}°")
        
        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="test_fix_result.jpg",
            show_all_lines=True
        )
        
        print(f"\n✅ 检测完成!")
        print(f"生成文件:")
        print(f"- test_fix_image.jpg: 测试图像")
        print(f"- test_fix_result.jpg: 检测结果")
        print(f"- debug_edges.jpg: 边缘检测结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_image():
    """
    测试真实图像
    """
    print(f"\n{'='*40}")
    print("测试真实图像")
    print("="*40)
    
    detector = ParallelLineDetector(
        angle_threshold=2.0,
        distance_threshold=100,
        min_line_length=50,
        max_line_gap=10
    )
    
    try:
        # 使用用户指定的图像
        image_path = "D:/1/2.jpg"
        print(f"检测图像: {image_path}")
        
        result = detector.detect_parallel_lines(image_path, debug=True)
        parallel_groups, original_image, edges, gray, all_lines = result
        
        print(f"\n=== 真实图像检测结果 ===")
        print(f"检测到的线条总数: {len(all_lines)}")
        print(f"平行线组数: {len(parallel_groups)}")
        
        if len(all_lines) > 100:
            print("⚠️  检测到过多线条，可能需要调整参数")
        elif len(all_lines) == 0:
            print("⚠️  未检测到任何线条，可能需要降低阈值")
        else:
            print("✅ 检测到的线条数量合理")
        
        if parallel_groups:
            total_parallel_lines = sum(len(group) for group in parallel_groups)
            print(f"平行线条数: {total_parallel_lines}")
            
            for i, group in enumerate(parallel_groups):
                angle = group[0][1]['angle']
                print(f"第{i+1}组: {len(group)}条线, 角度: {angle:.1f}°")
        
        # 保存结果
        detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="real_image_result.jpg",
            show_all_lines=True
        )
        
        print(f"\n✅ 真实图像检测完成!")
        print(f"结果保存到: real_image_result.jpg")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实图像检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    # 测试基本功能
    basic_success = test_basic_functionality()
    
    # 测试真实图像
    real_success = test_real_image()
    
    print(f"\n{'='*40}")
    print("测试总结:")
    print(f"基本功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"真实图像测试: {'✅ 通过' if real_success else '❌ 失败'}")
    
    if basic_success and real_success:
        print("🎉 所有测试通过！错误已修复。")
    elif basic_success:
        print("⚠️  基本功能正常，真实图像可能需要参数调优。")
    else:
        print("❌ 基本功能测试失败，需要进一步检查代码。")

if __name__ == "__main__":
    main()
