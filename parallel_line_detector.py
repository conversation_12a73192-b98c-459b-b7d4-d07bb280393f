import cv2
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import math
from typing import List, Tu<PERSON>, Dict

class ParallelLineDetector:
    def __init__(self, angle_threshold=3.0, distance_threshold=30, min_line_length=50, max_line_gap=10):
        """
        初始化平行线检测器

        Args:
            angle_threshold: 角度阈值（度），用于判断两条线是否平行
            distance_threshold: 距离阈值，用于判断平行线是否属于同一组
            min_line_length: 最小线段长度
            max_line_gap: 线段间最大间隙
        """
        self.angle_threshold = angle_threshold
        self.distance_threshold = distance_threshold
        self.min_line_length = min_line_length
        self.max_line_gap = max_line_gap
    
    def preprocess_image(self, image):
        """
        图像预处理：转换为灰度图并进行边缘检测，优化粗线条检测
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 对于粗线条，先进行形态学操作来细化线条
        # 使用开运算来分离粗线条的边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        opened = cv2.morphologyEx(blurred, cv2.MORPH_OPEN, kernel)

        # 自适应阈值的Canny边缘检测，优化以检测更多线条
        # 计算图像的中位数，用于自适应阈值
        median_val = np.median(opened)
        lower_threshold = int(max(0, 0.5 * median_val))  # 进一步降低下阈值
        upper_threshold = int(min(255, 1.5 * median_val))  # 提高上阈值

        # 确保阈值在合理范围内，偏向检测更多边缘
        lower_threshold = max(20, lower_threshold)  # 降低最小阈值
        upper_threshold = min(200, upper_threshold)  # 提高最大阈值

        print(f"Canny边缘检测阈值: {lower_threshold}, {upper_threshold}")

        edges = cv2.Canny(opened, lower_threshold, upper_threshold, apertureSize=3)

        # 计算边缘像素比例
        edge_pixels = np.sum(edges > 0)
        total_pixels = edges.shape[0] * edges.shape[1]
        edge_ratio = edge_pixels / total_pixels

        # 减少形态学操作以避免产生过多边缘
        # 只在必要时进行轻微的闭运算
        if edge_ratio > 0.05:  # 如果边缘太多，跳过形态学操作
            print("边缘较多，跳过形态学闭运算以减少噪声")
        else:
            kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 1))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
            # 重新计算边缘像素比例
            edge_pixels = np.sum(edges > 0)
            edge_ratio = edge_pixels / total_pixels

        print(f"边缘像素比例: {edge_ratio:.4f}")

        if edge_ratio < 0.01:  # 如果边缘太少
            print("边缘检测结果较少，降低阈值重新检测...")
            lower_threshold = max(15, lower_threshold // 2)
            upper_threshold = max(50, upper_threshold // 2)
            edges = cv2.Canny(opened, lower_threshold, upper_threshold, apertureSize=3)
            # 重新定义kernel_close以防之前未定义
            kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 1))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)

        return edges, gray

    def detect_lines(self, edges):
        """
        使用霍夫变换检测直线，减少误检测
        """
        # 使用更低的初始阈值来提高检测率
        # 优先保证检测到足够的线条
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=40)

        # 只有在检测到过多线条时才提高阈值
        if lines is not None and len(lines) > 150:
            print(f"检测到过多线条({len(lines)})，适度提高阈值...")
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=60)

        if lines is not None and len(lines) > 120:
            print(f"仍然过多线条({len(lines)})，进一步提高阈值...")
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=80)

        # 多层次检测策略，大幅提高检测率
        if lines is None or len(lines) < 15:
            print(f"当前检测到 {len(lines) if lines is not None else 0} 条线，尝试更敏感的检测...")
            # 尝试更低的阈值
            lines_low = cv2.HoughLines(edges, 1, np.pi/180, threshold=25)
            if lines_low is not None:
                if lines is None:
                    lines = lines_low
                else:
                    # 合并结果
                    lines = np.concatenate([lines, lines_low], axis=0)
                print(f"低阈值检测增加到总共 {len(lines)} 条线")

        # 如果还是不够，尝试极低阈值
        if lines is None or len(lines) < 10:
            print("进一步降低阈值检测...")
            lines_very_low = cv2.HoughLines(edges, 1, np.pi/180, threshold=15)
            if lines_very_low is not None:
                if lines is None:
                    lines = lines_very_low
                else:
                    lines = np.concatenate([lines, lines_very_low], axis=0)
                print(f"极低阈值检测增加到总共 {len(lines)} 条线")

        # 最后尝试超低阈值
        if lines is None or len(lines) < 5:
            print("使用超低阈值进行最后尝试...")
            lines_ultra_low = cv2.HoughLines(edges, 1, np.pi/180, threshold=10)
            if lines_ultra_low is not None:
                if lines is None:
                    lines = lines_ultra_low
                else:
                    lines = np.concatenate([lines, lines_ultra_low], axis=0)
                print(f"超低阈值检测增加到总共 {len(lines)} 条线")

        # 如果仍然检测不到足够的直线，尝试概率霍夫变换
        if lines is None or len(lines) < 8:
            print("尝试概率霍夫变换补充检测...")
            lines_p = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=15,
                                     minLineLength=max(10, self.min_line_length//4),
                                     maxLineGap=self.max_line_gap*4)

            if lines_p is not None:
                # 将概率霍夫变换的结果转换为标准格式
                converted_lines = []
                for line in lines_p:
                    x1, y1, x2, y2 = line[0]

                    # 计算直线的rho和theta参数
                    if x2 == x1:  # 垂直线
                        rho = abs(x1)
                        theta = np.pi/2 if x1 >= 0 else -np.pi/2
                    else:
                        # 计算直线方程 y = mx + b，转换为 rho*cos(theta) + rho*sin(theta) = rho
                        dx = x2 - x1
                        dy = y2 - y1

                        # 计算角度
                        theta = np.arctan2(dy, dx)
                        if theta < 0:
                            theta += np.pi

                        # 计算rho（原点到直线的距离）
                        rho = abs(x1 * np.sin(theta) - y1 * np.cos(theta))

                        # 确保rho为正值
                        if x1 * np.sin(theta) - y1 * np.cos(theta) < 0:
                            theta += np.pi
                            if theta >= np.pi:
                                theta -= np.pi

                    converted_lines.append([[rho, theta]])

                lines = np.array(converted_lines) if converted_lines else None

        if lines is None:
            print("未检测到任何直线")
            return []

        print(f"霍夫变换检测到 {len(lines)} 条直线")

        # 大幅提高过滤阈值，只在线条极多时才过滤
        if len(lines) > 200:  # 大幅提高过滤阈值
            print(f"检测到极多线条({len(lines)})，进行预过滤...")
            # 按照rho值排序，去除过于相近的线条
            lines_sorted = sorted(lines, key=lambda x: x[0][0])  # 按rho排序
            filtered_lines = []
            for line in lines_sorted:
                rho, theta = line[0]
                # 检查是否与已有线条过于相近，使用更严格的重复判断
                is_duplicate = False
                for existing_line in filtered_lines:
                    existing_rho, existing_theta = existing_line[0]
                    if abs(rho - existing_rho) < 2 and abs(theta - existing_theta) < 0.03:  # 非常严格的重复判断
                        is_duplicate = True
                        break
                if not is_duplicate:
                    filtered_lines.append(line)

            lines = np.array(filtered_lines)
            print(f"预过滤后剩余 {len(lines)} 条直线")
        else:
            print(f"检测到 {len(lines)} 条线条，保留所有线条以提高检测率")



        detected_lines = []
        for line in lines:
            rho, theta = line[0]

            # 计算角度（度）
            angle_rad = theta
            angle_deg = np.degrees(angle_rad)

            # 标准化角度到 [0, 180) 度
            if angle_deg < 0:
                angle_deg += 180
            elif angle_deg >= 180:
                angle_deg -= 180

            # 将极坐标转换为直线上的两点
            a = np.cos(theta)
            b = np.sin(theta)
            x0 = a * rho
            y0 = b * rho

            # 计算直线上的两个点（延伸到图像边界）
            x1 = int(x0 + 1000 * (-b))
            y1 = int(y0 + 1000 * (a))
            x2 = int(x0 - 1000 * (-b))
            y2 = int(y0 - 1000 * (a))

            # 计算直线方程 ax + by + c = 0
            if abs(b) < 1e-6:  # 垂直线
                line_a, line_b, line_c = 1, 0, -rho
            else:
                line_a = a
                line_b = b
                line_c = -rho

            detected_lines.append({
                'rho': rho,
                'theta': theta,
                'angle': angle_deg,
                'line_eq': (line_a, line_b, line_c),
                'points': (x1, y1, x2, y2)
            })

        # 过滤质量较差的线条
        filtered_lines = self.filter_low_quality_lines(detected_lines)
        print(f"质量过滤后剩余 {len(filtered_lines)} 条直线")

        return filtered_lines

    def filter_low_quality_lines(self, lines):
        """
        过滤质量较差的线条，使用更宽松的条件
        """
        if not lines:
            return lines

        # 只进行基本的质量检查，避免过度过滤
        filtered_lines = []
        for line in lines:
            # 只过滤掉明显异常的角度
            angle = line['angle']
            if not (0 <= angle <= 180):
                continue

            # 过滤掉明显异常的rho值（负值或过大值）
            rho = line['rho']
            if rho < 0 or rho > 10000:  # 只过滤极端异常值
                continue

            filtered_lines.append(line)

        print(f"质量过滤：{len(lines)} -> {len(filtered_lines)} 条线")
        return filtered_lines
    
    def line_to_cartesian(self, rho, theta):
        """
        将极坐标形式的直线转换为笛卡尔坐标系中的两个点
        """
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * rho
        
        # 计算直线上的两个点
        x1 = int(x0 + 1000 * (-b))
        y1 = int(y0 + 1000 * (a))
        x2 = int(x0 - 1000 * (-b))
        y2 = int(y0 - 1000 * (a))
        
        return (x1, y1), (x2, y2)
    
    def are_parallel(self, line1, line2):
        """
        判断两条直线是否平行，使用更宽松的条件
        """
        angle1 = line1['angle']
        angle2 = line2['angle']

        # 计算角度差
        angle_diff = abs(angle1 - angle2)

        # 考虑角度的周期性（0度和180度是同一方向）
        angle_diff = min(angle_diff, 180 - angle_diff)

        # 使用更宽松的角度阈值来检测更多平行线
        effective_threshold = self.angle_threshold * 1.5  # 增加50%的容忍度
        return angle_diff <= effective_threshold

    def calculate_distance_between_parallel_lines(self, line1, line2):
        """
        计算两条平行线之间的距离
        使用更准确的点到直线距离公式
        """
        # 获取直线方程参数 ax + by + c = 0
        a1, b1, c1 = line1['line_eq']
        _, _, c2 = line2['line_eq']  # 只需要c2，a2和b2与a1,b1相近

        # 对于平行线，使用点到直线距离公式
        # 距离 = |c1 - c2| / sqrt(a^2 + b^2)
        # 由于是平行线，a1≈a2, b1≈b2
        denominator = np.sqrt(a1*a1 + b1*b1)
        if denominator < 1e-6:
            # 如果分母太小，回退到rho差值方法
            return abs(line1['rho'] - line2['rho'])

        distance = abs(c1 - c2) / denominator
        return distance

    def merge_similar_lines(self, lines):
        """
        合并相似的线条以减少重复检测，特别处理粗线条
        """
        if not lines:
            return []

        merged_lines = []
        used = [False] * len(lines)

        for i, line1 in enumerate(lines):
            if used[i]:
                continue

            similar_lines = [line1]
            used[i] = True

            for j, line2 in enumerate(lines):
                if used[j] or i == j:
                    continue

                # 检查是否为相似线条
                angle_diff = abs(line1['angle'] - line2['angle'])
                angle_diff = min(angle_diff, 180 - angle_diff)

                # 计算距离
                distance = self.calculate_distance_between_parallel_lines(line1, line2)

                # 更宽松的合并条件，优先保留更多线条
                # 只合并非常相似的线条
                if angle_diff <= 3.0 and distance <= 20:  # 更宽松的合并条件
                    similar_lines.append(line2)
                    used[j] = True
                    print(f"  合并相似线条: 角度差={angle_diff:.1f}°, 距离={distance:.1f}")

            # 选择rho值的平均值作为代表
            if len(similar_lines) > 1:
                print(f"  将 {len(similar_lines)} 条相似线条合并为1条")
                avg_rho = np.mean([line['rho'] for line in similar_lines])
                avg_theta = np.mean([line['theta'] for line in similar_lines])
                avg_angle = np.mean([line['angle'] for line in similar_lines])

                # 重新计算直线方程
                a = np.cos(avg_theta)
                b = np.sin(avg_theta)
                c = -avg_rho

                x1 = int(a * avg_rho + 1000 * (-b))
                y1 = int(b * avg_rho + 1000 * (a))
                x2 = int(a * avg_rho - 1000 * (-b))
                y2 = int(b * avg_rho - 1000 * (a))

                merged_line = {
                    'rho': avg_rho,
                    'theta': avg_theta,
                    'angle': avg_angle,
                    'line_eq': (a, b, c),
                    'points': (x1, y1, x2, y2),
                    'merged_count': len(similar_lines)  # 记录合并的线条数量
                }
                merged_lines.append(merged_line)
            else:
                line1['merged_count'] = 1
                merged_lines.append(line1)

        return merged_lines

    def group_parallel_lines(self, lines):
        """
        将检测到的直线按平行关系分组
        """
        if not lines:
            return []

        # 首先合并相似的线条
        merged_lines = self.merge_similar_lines(lines)

        if len(merged_lines) < 2:
            print(f"合并后只剩 {len(merged_lines)} 条线，无法形成平行线组")
            return []

        # 如果线条数量仍然较少，放宽检测条件
        if len(merged_lines) < 6:
            print(f"线条数量较少({len(merged_lines)})，将使用更宽松的平行线检测条件")

        print(f"合并后剩余 {len(merged_lines)} 条线")

        # 打印所有线条的角度信息用于调试
        for i, line in enumerate(merged_lines):
            print(f"  线条 {i}: 角度={line['angle']:.1f}°, rho={line['rho']:.1f}")

        parallel_groups = []
        used = [False] * len(merged_lines)

        # 对每条线寻找平行线
        for i, line1 in enumerate(merged_lines):
            if used[i]:
                continue

            current_group = [(i, line1)]
            used[i] = True

            # 寻找与当前线平行的其他线
            for j, line2 in enumerate(merged_lines):
                if used[j] or i == j:
                    continue

                # 检查是否平行
                if self.are_parallel(line1, line2):
                    # 计算距离
                    distance = self.calculate_distance_between_parallel_lines(line1, line2)

                    print(f"  线条 {i} 和 {j} 平行，距离={distance:.1f}")

                    # 大幅放宽距离要求，优先检测到更多平行线
                    # 根据检测到的线条数量动态调整
                    if len(merged_lines) < 10:
                        min_distance = 2   # 极宽松的最小距离
                        max_distance = self.distance_threshold * 3  # 大幅增加最大距离
                    else:
                        min_distance = 3   # 宽松的最小距离
                        max_distance = self.distance_threshold * 2  # 增加最大距离

                    if distance >= min_distance and distance <= max_distance:
                        current_group.append((j, line2))
                        used[j] = True
                        print(f"    -> 加入同一组 (距离: {distance:.1f}, 要求: {min_distance}-{max_distance})")
                    else:
                        print(f"    -> 距离不符合要求 ({min_distance} <= {distance:.1f} <= {max_distance})")

            # 只保留包含至少2条线的组
            if len(current_group) >= 2:
                parallel_groups.append(current_group)
                print(f"  形成平行线组: {len(current_group)} 条线")

        # 总是使用改进的聚类方法，因为原方法在多条平行线时有问题
        print("使用改进的聚类方法进行分组...")
        improved_groups = self.cluster_parallel_lines_improved(merged_lines)

        # 如果改进方法检测到更多组，使用改进结果
        if len(improved_groups) > len(parallel_groups):
            print(f"改进方法检测到更多组({len(improved_groups)} vs {len(parallel_groups)})，使用改进结果")
            parallel_groups = improved_groups

        return parallel_groups

    def cluster_parallel_lines_improved(self, lines):
        """
        完全重写的平行线聚类方法，专门处理多条平行线的情况
        """
        if len(lines) < 2:
            return []

        print(f"开始改进聚类，输入 {len(lines)} 条线")

        # 第一步：按角度进行粗分组，使用更精确的角度处理
        angle_groups = self.group_by_angle(lines)
        print(f"按角度分组得到 {len(angle_groups)} 个角度组")

        # 第二步：对每个角度组内的线条按距离进行精细分组
        final_groups = []
        for angle_key, angle_group in angle_groups.items():
            if len(angle_group) < 2:
                continue

            print(f"处理角度组 {angle_key:.1f}°，包含 {len(angle_group)} 条线")
            distance_groups = self.group_by_distance(angle_group)
            final_groups.extend(distance_groups)

        print(f"改进聚类最终检测到 {len(final_groups)} 组平行线")
        return final_groups

    def group_by_angle(self, lines):
        """
        按角度对线条进行分组
        """
        angle_groups = {}
        angle_tolerance = self.angle_threshold * 1.8  # 适中的角度容忍度

        for i, line in enumerate(lines):
            angle = line['angle']

            # 标准化角度：将所有角度映射到 [0, 90) 范围
            # 因为平行线的角度可能相差180度
            if angle >= 90:
                normalized_angle = angle - 90
            else:
                normalized_angle = angle

            # 寻找相近角度的组
            found_group = False
            best_match_angle = None
            min_angle_diff = float('inf')

            for group_angle in angle_groups.keys():
                angle_diff = abs(normalized_angle - group_angle)
                # 处理角度的周期性（0度和90度相近）
                angle_diff = min(angle_diff, 90 - angle_diff)

                if angle_diff <= angle_tolerance and angle_diff < min_angle_diff:
                    min_angle_diff = angle_diff
                    best_match_angle = group_angle
                    found_group = True

            if found_group:
                angle_groups[best_match_angle].append((i, line))
            else:
                angle_groups[normalized_angle] = [(i, line)]

        return angle_groups

    def group_by_distance(self, angle_group):
        """
        对同一角度组内的线条按距离进行分组
        """
        if len(angle_group) < 2:
            return []

        # 按rho值排序，便于距离分组
        sorted_lines = sorted(angle_group, key=lambda x: x[1]['rho'])

        distance_groups = []
        used = [False] * len(sorted_lines)

        for i, (idx1, line1) in enumerate(sorted_lines):
            if used[i]:
                continue

            # 开始一个新的距离组
            current_group = [(idx1, line1)]
            used[i] = True

            # 寻找距离合适的其他线条
            for j, (idx2, line2) in enumerate(sorted_lines):
                if used[j] or i == j:
                    continue

                # 计算与当前组中所有线条的距离
                distances_to_group = []
                for _, group_line in current_group:
                    dist = self.calculate_distance_between_parallel_lines(group_line, line2)
                    distances_to_group.append(dist)

                min_dist = min(distances_to_group)
                max_dist = max(distances_to_group)

                # 动态调整距离阈值
                # 如果是密集的平行线，使用较小的阈值
                # 如果是稀疏的平行线，使用较大的阈值
                if len(sorted_lines) > 5:  # 密集情况
                    max_allowed_distance = self.distance_threshold * 1.5
                    min_allowed_distance = 2
                else:  # 稀疏情况
                    max_allowed_distance = self.distance_threshold * 3
                    min_allowed_distance = 1

                # 检查是否可以加入当前组
                if (min_allowed_distance <= min_dist <= max_allowed_distance and
                    max_dist <= max_allowed_distance * 1.2):  # 确保组内距离不会过大

                    current_group.append((idx2, line2))
                    used[j] = True
                    print(f"      线条 {idx2} 加入组 (距离范围: {min_dist:.1f}-{max_dist:.1f})")

            # 只保留包含至少2条线的组
            if len(current_group) >= 2:
                distance_groups.append(current_group)
                rho_values = [line[1]['rho'] for line in current_group]
                print(f"    形成距离组: {len(current_group)} 条线, rho范围: {min(rho_values):.1f}-{max(rho_values):.1f}")

        return distance_groups

    def detect_parallel_lines(self, image_path, debug=True):
        """
        主函数：检测图片中的平行线组
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        print(f"图像尺寸: {image.shape}")

        # 预处理
        edges, gray = self.preprocess_image(image)

        if debug:
            # 保存边缘检测结果用于调试
            cv2.imwrite("debug_edges.jpg", edges)
            print("边缘检测结果已保存到 debug_edges.jpg")

        # 检测直线
        lines = self.detect_lines(edges)

        if not lines:
            print("未检测到任何直线")
            if debug:
                print("调试建议:")
                print("1. 检查图像对比度是否足够")
                print("2. 尝试调整Canny边缘检测参数")
                print("3. 降低霍夫变换阈值")
                print("4. 检查图像中是否确实存在直线")
            return [], image, edges, gray, []

        print(f"检测到 {len(lines)} 条线段")

        # 分组平行线
        parallel_groups = self.group_parallel_lines(lines)

        print(f"检测到 {len(parallel_groups)} 组平行线")

        # 输出每组的详细信息
        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']  # 获取第一条线的角度
            print(f"  第 {i+1} 组: {len(group)} 条平行线, 角度: {angle:.1f}°")

        return parallel_groups, image, edges, gray, lines
    
    def clip_line_to_image(self, x1, y1, x2, y2, width, height):
        """
        将直线裁剪到图像边界内
        """
        # 使用Liang-Barsky算法裁剪直线
        def clip_test(p, q, u1, u2):
            if p < 0:
                r = q / p
                if r > u2:
                    return False, u1, u2
                elif r > u1:
                    u1 = r
            elif p > 0:
                r = q / p
                if r < u1:
                    return False, u1, u2
                elif r < u2:
                    u2 = r
            elif q < 0:
                return False, u1, u2
            return True, u1, u2

        dx = x2 - x1
        dy = y2 - y1
        u1, u2 = 0.0, 1.0

        # 左边界
        accept, u1, u2 = clip_test(-dx, x1, u1, u2)
        if not accept:
            return None

        # 右边界
        accept, u1, u2 = clip_test(dx, width - 1 - x1, u1, u2)
        if not accept:
            return None

        # 下边界
        accept, u1, u2 = clip_test(-dy, y1, u1, u2)
        if not accept:
            return None

        # 上边界
        accept, u1, u2 = clip_test(dy, height - 1 - y1, u1, u2)
        if not accept:
            return None

        if u2 < 1:
            x2 = x1 + u2 * dx
            y2 = y1 + u2 * dy

        if u1 > 0:
            x1 = x1 + u1 * dx
            y1 = y1 + u1 * dy

        return int(x1), int(y1), int(x2), int(y2)

    def visualize_results(self, image, parallel_groups, all_lines=None, save_path=None, show_all_lines=False):
        """
        可视化检测结果
        """
        result_image = image.copy()
        height, width = image.shape[:2]

        # 为每组平行线分配不同的颜色
        colors = [
            (0, 0, 255),    # 红色 (BGR格式)
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 255, 255),  # 黄色
            (255, 0, 255),  # 品红色
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (0, 165, 255),  # 橙色
            (128, 128, 0),  # 橄榄色
            (128, 0, 0),    # 栗色
        ]

        # 如果需要显示所有检测到的线条（灰色）
        if show_all_lines and all_lines:
            for line in all_lines:
                x1, y1, x2, y2 = line['points']
                clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                if clipped:
                    cv2.line(result_image, (clipped[0], clipped[1]),
                            (clipped[2], clipped[3]), (128, 128, 128), 5)  # 进一步增加背景线条粗细到5像素

        # 绘制平行线组
        for group_idx, group in enumerate(parallel_groups):
            color = colors[group_idx % len(colors)]

            for _, line in group:
                x1, y1, x2, y2 = line['points']
                clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                if clipped:
                    cv2.line(result_image, (clipped[0], clipped[1]),
                            (clipped[2], clipped[3]), color, 12)  # 进一步增加线条粗细到12像素

        # 添加文本标注
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(result_image, f'Parallel Groups: {len(parallel_groups)}',
                   (10, 35), font, 1.2, (255, 255, 255), 3)  # 增加字体大小和粗细

        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']  # 获取第一条线的角度
            text = f'Group {i+1}: {len(group)} lines, {angle:.1f}°'
            cv2.putText(result_image, text, (10, 70 + i * 30),  # 调整间距
                       font, 0.8, colors[i % len(colors)], 3)  # 增加字体大小和粗细

        if save_path:
            cv2.imwrite(save_path, result_image)
            print(f"结果图像已保存到: {save_path}")

        return result_image

    def count_parallel_pairs(self, parallel_groups: List[List[Tuple[int, dict]]]) -> Tuple[int, List[int]]:
        """
        计算平行线对数：每组含 n 条线，其平行线对数为 C(n, 2) = n*(n-1)/2。
        返回总对数与各组对数列表。
        """
        group_pairs = []
        for group in parallel_groups:
            n = len(group)
            group_pairs.append(n * (n - 1) // 2)
        total_pairs = sum(group_pairs)
        return total_pairs, group_pairs

def create_test_image():
    """
    创建一个包含平行线的测试图像
    """
    # 创建一个白色背景的图像
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 绘制第一组平行线（水平方向）
    y_positions = [100, 150, 200]
    for y in y_positions:
        cv2.line(image, (50, y), (750, y), (0, 0, 0), 3)

    # 绘制第二组平行线（倾斜方向）
    start_points = [(100, 300), (150, 350), (200, 400)]
    end_points = [(600, 400), (650, 450), (700, 500)]
    for start, end in zip(start_points, end_points):
        cv2.line(image, start, end, (0, 0, 0), 3)

    # 绘制第三组平行线（垂直方向）
    x_positions = [300, 350, 400]
    for x in x_positions:
        cv2.line(image, (x, 50), (x, 250), (0, 0, 0), 3)

    # 添加一些噪声线（非平行）
    cv2.line(image, (500, 100), (600, 300), (128, 128, 128), 2)
    cv2.line(image, (50, 500), (200, 550), (128, 128, 128), 2)

    return image

def main():
    """
    主函数示例
    """
    print("=" * 60)
    print("🔍 平行线检测算法演示")
    print("=" * 60)

    # 创建测试图像
    print("创建测试图像...")
    test_image = create_test_image()
    test_image_path = "D:/1/3.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"✅ 测试图像已保存: {test_image_path}")

    # 创建检测器实例
    detector = ParallelLineDetector(
        angle_threshold=3.0,
        distance_threshold=40,
        min_line_length=30,
        max_line_gap=10
    )

    try:
        print(f"\n开始检测平行线...")
        parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(test_image_path)

        # 输出结果
        print(f"\n=== 平行线检测结果 ===")
        print(f"总共检测到 {len(parallel_groups)} 组平行线")

        total_parallel_lines = sum(len(group) for group in parallel_groups)
        total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
        print(f"平行线条数: {total_parallel_lines}")
        print(f"平行线对数: {total_pairs}")
        for idx, (group, pair_cnt) in enumerate(zip(parallel_groups, group_pairs)):
            print(f"  第{idx+1}组: {len(group)} 条线, {pair_cnt} 对")

        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="parallel_lines_result.jpg",
            show_all_lines=True
        )

        # 使用matplotlib显示结果
        plt.figure(figsize=(20, 5))

        plt.subplot(1, 4, 1)
        plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        plt.title("原始图像")
        plt.axis('off')

        plt.subplot(1, 4, 2)
        plt.imshow(gray, cmap='gray')
        plt.title("灰度图像")
        plt.axis('off')

        plt.subplot(1, 4, 3)
        plt.imshow(edges, cmap='gray')
        plt.title("边缘检测")
        plt.axis('off')

        plt.subplot(1, 4, 4)
        plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        plt.title(f"平行线检测结果\n({len(parallel_groups)} 组平行线)")
        plt.axis('off')

        plt.tight_layout()
        plt.savefig("detection_process.png", dpi=150, bbox_inches='tight')
        plt.show()

        print(f"\n✅ 检测完成!")
        print(f"📊 结果图像: parallel_lines_result.jpg")
        print(f"📊 处理过程: detection_process.png")

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
