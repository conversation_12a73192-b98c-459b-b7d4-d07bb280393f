#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多条平行线检测的改进算法
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector

def create_multiple_parallel_test():
    """
    创建包含多组平行线的测试图像
    """
    height, width = 500, 700
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 第一组：5条水平平行线
    y_positions = [80, 120, 160, 200, 240]
    for y in y_positions:
        cv2.line(image, (50, y), (650, y), (0, 0, 0), 3)
    
    # 第二组：4条垂直平行线
    x_positions = [150, 200, 250, 300]
    for x in x_positions:
        cv2.line(image, (x, 280), (x, 450), (0, 0, 0), 3)
    
    # 第三组：3条倾斜平行线（约30度）
    starts = [(400, 100), (450, 100), (500, 100)]
    ends = [(500, 200), (550, 200), (600, 200)]
    for start, end in zip(starts, ends):
        cv2.line(image, start, end, (0, 0, 0), 3)
    
    # 第四组：6条密集的水平平行线
    y_dense = [320, 335, 350, 365, 380, 395]
    for y in y_dense:
        cv2.line(image, (400, y), (650, y), (0, 0, 0), 2)
    
    return image

def create_complex_parallel_test():
    """
    创建复杂的多条平行线测试图像
    """
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    
    # 第一组：7条水平平行线，间距不均匀
    y_positions = [60, 90, 130, 160, 200, 250, 290]
    for y in y_positions:
        cv2.line(image, (30, y), (770, y), (0, 0, 0), 3)
    
    # 第二组：5条垂直平行线
    x_positions = [100, 200, 300, 400, 500]
    for x in x_positions:
        cv2.line(image, (x, 320), (x, 580), (0, 0, 0), 3)
    
    # 第三组：4条倾斜平行线（约45度）
    starts = [(550, 50), (580, 50), (610, 50), (640, 50)]
    ends = [(650, 150), (680, 150), (710, 150), (740, 150)]
    for start, end in zip(starts, ends):
        cv2.line(image, start, end, (0, 0, 0), 3)
    
    # 第四组：3条倾斜平行线（约-30度）
    starts = [(50, 400), (80, 400), (110, 400)]
    ends = [(150, 500), (180, 500), (210, 500)]
    for start, end in zip(starts, ends):
        cv2.line(image, start, end, (0, 0, 0), 3)
    
    return image

def test_multiple_parallel_detection():
    """
    测试多条平行线检测
    """
    print("🔍 测试多条平行线检测算法")
    print("="*50)
    
    # 创建检测器，使用适合多条平行线的参数
    detector = ParallelLineDetector(
        angle_threshold=3.0,
        distance_threshold=150,  # 增加距离阈值以处理间距较大的平行线
        min_line_length=30,
        max_line_gap=15
    )
    
    # 测试1：多组平行线
    print("\n测试1: 多组平行线")
    multiple_image = create_multiple_parallel_test()
    cv2.imwrite("multiple_parallel_test.jpg", multiple_image)
    
    try:
        result = detector.detect_parallel_lines("multiple_parallel_test.jpg", debug=True)
        parallel_groups, image, edges, gray, all_lines = result
        
        print(f"\n=== 多组平行线检测结果 ===")
        print(f"检测到的线条总数: {len(all_lines)}")
        print(f"平行线组数: {len(parallel_groups)}")
        print(f"期望组数: 4 (水平5条 + 垂直4条 + 倾斜3条 + 密集6条)")
        
        if parallel_groups:
            total_parallel_lines = sum(len(group) for group in parallel_groups)
            total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
            print(f"平行线条数: {total_parallel_lines}")
            print(f"平行线对数: {total_pairs}")
            
            for idx, (group, pair_cnt) in enumerate(zip(parallel_groups, group_pairs)):
                angle = group[0][1]['angle']
                distances = []
                for i in range(len(group)):
                    for j in range(i+1, len(group)):
                        dist = detector.calculate_distance_between_parallel_lines(
                            group[i][1], group[j][1]
                        )
                        distances.append(dist)
                
                avg_distance = np.mean(distances) if distances else 0
                print(f"第{idx+1}组: {len(group)}条线, 角度{angle:.1f}°, 平均间距{avg_distance:.1f}px, {pair_cnt}对")
        
        # 可视化结果
        result_img = detector.visualize_results(
            image, parallel_groups, all_lines, 
            "multiple_parallel_result.jpg", show_all_lines=True
        )
        
        success1 = len(parallel_groups) >= 3  # 至少检测到3组
        
    except Exception as e:
        print(f"错误: {e}")
        success1 = False
    
    # 测试2：复杂平行线
    print(f"\n{'='*50}")
    print("测试2: 复杂多条平行线")
    complex_image = create_complex_parallel_test()
    cv2.imwrite("complex_parallel_test.jpg", complex_image)
    
    try:
        result = detector.detect_parallel_lines("complex_parallel_test.jpg", debug=True)
        parallel_groups, image, edges, gray, all_lines = result
        
        print(f"\n=== 复杂平行线检测结果 ===")
        print(f"检测到的线条总数: {len(all_lines)}")
        print(f"平行线组数: {len(parallel_groups)}")
        print(f"期望组数: 4 (水平7条 + 垂直5条 + 倾斜4条 + 倾斜3条)")
        
        if parallel_groups:
            total_parallel_lines = sum(len(group) for group in parallel_groups)
            print(f"平行线条数: {total_parallel_lines}")
            
            for idx, group in enumerate(parallel_groups):
                angle = group[0][1]['angle']
                print(f"第{idx+1}组: {len(group)}条线, 角度{angle:.1f}°")
        
        # 可视化结果
        result_img = detector.visualize_results(
            image, parallel_groups, all_lines, 
            "complex_parallel_result.jpg", show_all_lines=True
        )
        
        success2 = len(parallel_groups) >= 3  # 至少检测到3组
        
        # 创建对比图
        plt.figure(figsize=(20, 10))
        
        # 多组平行线测试
        plt.subplot(2, 4, 1)
        plt.imshow(cv2.cvtColor(multiple_image, cv2.COLOR_BGR2RGB))
        plt.title("多组平行线测试图")
        plt.axis('off')
        
        plt.subplot(2, 4, 2)
        multiple_result = cv2.imread("multiple_parallel_result.jpg")
        if multiple_result is not None:
            plt.imshow(cv2.cvtColor(multiple_result, cv2.COLOR_BGR2RGB))
        plt.title("多组平行线检测结果")
        plt.axis('off')
        
        # 复杂平行线测试
        plt.subplot(2, 4, 3)
        plt.imshow(cv2.cvtColor(complex_image, cv2.COLOR_BGR2RGB))
        plt.title("复杂平行线测试图")
        plt.axis('off')
        
        plt.subplot(2, 4, 4)
        plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        plt.title("复杂平行线检测结果")
        plt.axis('off')
        
        # 真实图像测试
        plt.subplot(2, 4, 5)
        real_image_path = "D:/1/3.jpg"
        try:
            real_result = detector.detect_parallel_lines(real_image_path, debug=False)
            real_parallel_groups, real_image, _, _, real_all_lines = real_result
            
            plt.imshow(cv2.cvtColor(real_image, cv2.COLOR_BGR2RGB))
            plt.title("真实图像")
            plt.axis('off')
            
            plt.subplot(2, 4, 6)
            real_result_img = detector.visualize_results(
                real_image, real_parallel_groups, real_all_lines, 
                "real_multiple_result.jpg", show_all_lines=True
            )
            plt.imshow(cv2.cvtColor(real_result_img, cv2.COLOR_BGR2RGB))
            plt.title(f"真实图像结果\n({len(real_parallel_groups)}组)")
            plt.axis('off')
            
            print(f"\n=== 真实图像检测结果 ===")
            print(f"检测到的线条总数: {len(real_all_lines)}")
            print(f"平行线组数: {len(real_parallel_groups)}")
            
        except Exception as e:
            print(f"真实图像测试失败: {e}")
        
        plt.tight_layout()
        plt.savefig("multiple_parallel_comparison.png", dpi=150, bbox_inches='tight')
        plt.show()
        
        print(f"\n✅ 测试完成!")
        print(f"生成文件:")
        print(f"- multiple_parallel_test.jpg: 多组平行线测试图")
        print(f"- complex_parallel_test.jpg: 复杂平行线测试图")
        print(f"- multiple_parallel_result.jpg: 多组平行线检测结果")
        print(f"- complex_parallel_result.jpg: 复杂平行线检测结果")
        print(f"- real_multiple_result.jpg: 真实图像检测结果")
        print(f"- multiple_parallel_comparison.png: 对比图")
        
        return success2
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主测试函数
    """
    success = test_multiple_parallel_detection()
    
    print(f"\n{'='*50}")
    print("测试总结:")
    print(f"多条平行线检测: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("🎉 多条平行线检测算法改进成功！")
    else:
        print("⚠️  多条平行线检测需要进一步优化。")

if __name__ == "__main__":
    main()
