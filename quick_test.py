import cv2
import numpy as np

# 创建简单测试图像
def create_test():
    img = np.ones((300, 400, 3), dtype=np.uint8) * 255
    
    # 第一组：3条水平平行线
    cv2.line(img, (50, 50), (350, 50), (0, 0, 0), 3)
    cv2.line(img, (50, 100), (350, 100), (0, 0, 0), 3)
    cv2.line(img, (50, 150), (350, 150), (0, 0, 0), 3)
    
    # 第二组：2条垂直平行线
    cv2.line(img, (100, 200), (100, 280), (0, 0, 0), 3)
    cv2.line(img, (200, 200), (200, 280), (0, 0, 0), 3)
    
    cv2.imwrite("simple_test.jpg", img)
    return img

# 测试
if __name__ == "__main__":
    create_test()
    
    from parallel_line_detector import ParallelLineDetector
    
    detector = ParallelLineDetector(angle_threshold=5.0, distance_threshold=80)
    
    try:
        result = detector.detect_parallel_lines("simple_test.jpg")
        parallel_groups, image, edges, gray, all_lines = result
        
        print(f"检测到 {len(parallel_groups)} 组平行线")
        for i, group in enumerate(parallel_groups):
            print(f"第{i+1}组: {len(group)}条线, 角度: {group[0][1]['angle']:.1f}°")
        
        # 保存结果
        detector.visualize_results(image, parallel_groups, all_lines, 
                                 "result.jpg", show_all_lines=True)
        print("结果已保存到 result.jpg")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
